.courses-container {
  padding: 24px;
  background-color: var(--bg-gray);
  min-height: 100vh;
}

/* Stats Cards */
.courses-container .dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.courses-container .stat-card {
  position: relative;
  border-radius: 16px;
  padding: 24px;
  overflow: hidden;
}

.courses-container .stat-card.schools {
  background-color: #ffecef;
}

.courses-container .stat-card.educators {
  background-color: #e8ffef;
}

.courses-container .stat-card.courses {
  background-color: #fff8e8;
}

.courses-container .stat-count {
  font-size: 28px;
  font-weight: 600;
  margin: 8px 0;
  color: #333;
}

.courses-container .stat-title {
  font-size: 14px;
  color: #666;
}

.courses-container .stat-icon1 {
  background-color: #fa5a7d;
  border-radius: 50%;
  padding: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.courses-container .stat-icon2 {
  background-color: #3cd856;
  border-radius: 50%;
  padding: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.courses-container .stat-icon3 {
  background-color: #ff947a;
  border-radius: 50%;
  padding: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.courses-container .icondesign1,
.courses-container .icondesign2,
.courses-container .icondesign3 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 80px;
  opacity: 0.1;
}

/* Add padding to match AdminDashboard */
.courses-container.admin-dashboard.courses-container {
  padding: 0px;
}

/* Section Header */
.courses-container .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.courses-container .section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.courses-container .header-actions {
  display: flex;
  gap: 12px;
}

.courses-container .add-course-btn,
.courses-container .add-educator-btn,
.courses-container .view-all-btn {
  padding: 8px 16px;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.courses-container .add-course-btn {
  background-color: var(--bg-primary);
  color: white;
  border: none;
}
.courses-container .add-educator-btn {
  background-color: var(--bg-primary);
  color: white;
  border: none;
}
.courses-container .view-all-btn {
  background-color: transparent;
  color: var(--bg-primary);
  border: 1px solid var(--bg-primary);
}

.courses-container .add-course-btn:hover {
  background-color: #4338ca;
}

.courses-container .view-all-btn:hover {
  background-color: #eef2ff;
}

/* Legacy header styles for backward compatibility */
.courses-container .courses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.courses-container .courses-title {
  font-size: var(--heading4);
  color: var(--text-color);
  font-weight: 500;
}

.courses-container .search-filter-container {
  display: grid;
  grid-template-columns: 6fr 3fr 2fr 2fr;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.courses-container .search-input {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.courses-container .search-input:focus {
  border-color: var(--bg-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.courses-container .filter-select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  background-color: white;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

.courses-container .filter-select:focus {
  border-color: var(--bg-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Table styles */
.courses-container .table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  margin-bottom: 1rem;
}

/* DataTable Customization */
.courses-container .rdt_Table {
  background-color: white;

  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.courses-container .rdt_TableHeader {
  display: none;
}

.courses-container .rdt_TableHead {
  background-color: #f9fafb;
}

.courses-container .rdt_TableHeadRow {
  border-bottom: 1px solid #e5e7eb;
}

.courses-container .rdt_TableCol {
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.courses-container .rdt_TableRow {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.courses-container .rdt_TableRow:hover {
  background-color: #f9fafb;
}

.courses-container .rdt_TableCell {
  padding: 16px;
  font-size: 14px;
  color: #374151;
}

.courses-container .rdt_Pagination {
  border-top: 1px solid #e5e7eb;
  padding: 12px;
}

/* Course info and Professor info */
.courses-container .course-info,
.courses-container .professor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Draft badge styling */
.courses-container .draft-badge {
  display: inline-block;
  padding: 3px 8px;
  background-color: #f3f4f6;
  color: #6b7280;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
  border: 1px solid #d1d5db;
}

/* Publication status styling */
.courses-container .publication-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.courses-container .publication-status.draft {
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.courses-container .publication-status.published {
  background-color: #ecfdf5;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.courses-container .course-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: var(--border-small-radius);
  object-fit: contain;
}

.courses-container .professor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

/* Status Indicator */
.courses-container .status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.courses-container .status-indicator {
  width: 36px;
  height: 20px;
  border-radius: 20px;
  background-color: #e5e7eb;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.courses-container .status-indicator::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.courses-container .status-indicator.active {
  background-color: var(--bg-primary);
}

.courses-container .status-indicator.active::before {
  transform: translateX(16px);
}

.courses-container .text-green-600 {
  color: #16a34a;
  font-size: 14px;
  font-weight: 500;
}

.courses-container .text-red-600 {
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

/* Action Buttons */
.courses-container .action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.courses-container .action-btn,
.courses-container .action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.courses-container .action-btn.view {
  background-color: #eef2ff;
  color: var(--bg-primary);
}

.courses-container .action-btn.edit {
  background-color: #e7eee5;
  color: #4abb66;
}

.courses-container .action-btn.edit,
.courses-container .action-button.edit {
  background-color: #ecfdf5;
  color: #059669;
}

.courses-container .action-btn.delete,
.courses-container .action-button.delete {
  background-color: #fef2f2;
  color: #dc2626;
}

.courses-container .action-btn.add,
.courses-container .action-button.add {
  background-color: #e7eee5;
  color: #4abb66;
}

.courses-container .action-btn:hover,
.courses-container .action-button:hover {
  transform: translateY(-1px);
}

.courses-container .action-btn svg,
.courses-container .action-button svg {
  width: 14px;
  height: 14px;
}

/* Apply AdminDashboard styles to course cards */
.courses-container .admin-dashboard .course-title {
  font-size: var(--smallfont);
  font-weight: 6 00;
  margin-bottom: 5px;
  color: var(--text-color);
}

.courses-container .admin-dashboard .course-category {
  font-size: var(--extrasmallfont);
  color: var(--text-primary);
  margin-bottom: 10px;
  font-weight: 400;
}

.courses-container .admin-dashboard .course-description {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  margin-bottom: 15px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;
  padding-bottom: 0px;
}

.courses-container .admin-dashboard .instructor {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.courses-container .admin-dashboard .instructor-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}

.courses-container .admin-dashboard .instructor-name {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.courses-container .admin-dashboard .course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  margin-bottom: 15px;
}

.courses-container .admin-dashboard .meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.courses-container .admin-dashboard .enroll-btn {
  display: block;
  width: 100%;
  padding: 8px 0;
  text-align: center;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.courses-container .admin-dashboard .enroll-btn:hover {
  background-color: var(--primary-light-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .courses-container {
    padding: 20px;
  }

  .courses-container .search-filter-container {
    grid-template-columns: 5fr 2fr 2fr 2fr;
    gap: 12px;
  }
}

@media (max-width: 992px) {
  .courses-container .search-filter-container {
    grid-template-columns: 3fr 2fr 2fr 2fr;
  }

  .courses-container .section-header {
    flex-wrap: wrap;
    gap: 16px;
  }

  .courses-container .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 900px) {
  .courses-container .search-filter-container {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .courses-container .search-input {
    grid-column: span 3;
  }
}

@media (max-width: 768px) {
  .courses-container {
    padding: 16px;
  }

  .courses-container .dashboard-stats {
    grid-template-columns: 1fr 1fr;
  }

  .courses-container .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .courses-container .header-actions {
    margin-top: 12px;
  }

  .courses-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
  }

  .courses-container .search-input {
    grid-column: span 2;
  }
}

@media (max-width: 576px) {
  .courses-container {
    padding: 12px;
  }

  .courses-container .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .courses-container .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .courses-container .add-course-btn,
  .courses-container .view-all-btn {
    width: 100%;
    text-align: center;
  }

  .courses-container .search-filter-container {
    grid-template-columns: 1fr;
  }

  .courses-container .search-input,
  .courses-container .filter-select {
    grid-column: span 1;
  }
}

/* Course Cards Layout - For Tutor Dashboard */
.course-cards-container {
  width: 100%;
  margin-bottom: 2rem;
}

.course-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.course-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.course-card-thumbnail {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.course-card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.course-card:hover .course-card-thumbnail img {
  transform: scale(1.05);
}

.course-card-content {
  padding: 10px 20px;
  flex-grow: 1;
}

.course-card-category {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-card-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.course-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  margin-right: 8px;
}

.course-card-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.course-card-footer {
  padding: 10px 20px;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.course-card-professor {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.professor-avatar-small {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 8px;
}

.course-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.course-card-meta-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.meta-icon {
  margin-right: 6px;
  font-size: 14px;
  color: var(--bg-primary);
}

.course-card-actions {
  display: flex;
  justify-content: center;
}

.resume-btn,
.start-learning-btn {
  width: 100%;
  padding: 10px;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resume-btn {
  background-color: white;
  color: var(--bg-primary);
  border: 1px solid var(--bg-primary);
}

.start-learning-btn {
  background-color: var(--bg-primary);
  color: white;
  border: none;
}

.resume-btn:hover {
  background-color: #f0f4ff;
}

.start-learning-btn:hover {
  background-color: #4338ca;
}

@media (max-width: 768px) {
  .course-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 576px) {
  .course-cards-grid {
    grid-template-columns: 1fr;
  }
}
