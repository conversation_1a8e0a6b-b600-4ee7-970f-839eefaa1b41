/* CourseCreationFlow.css */
/* Main container styles */
.course-creation-container {
  min-height: 100vh;
  margin: 0 auto;
  font-family: var(--font-Poppins);
  color: var(--text-color);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  padding: 8px 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--bg-gray);
  color: var(--text-color);
}

.header h1 {
  margin-left: 16px;
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--text-primary);
}

/* Enhanced Stepper Container */
.stepper-container {
  margin: 0 auto 30px;
  padding: 0 10px;
}

.stepper {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
}

.stepper::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--border-gray);
  z-index: 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-white);
  border: 2px solid var(--border-gray);
  color: var(--text-gray);
  font-weight: 600;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step.active .step-number {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 5px rgba(var(--primary-color-rgb), 0.1);
  transform: scale(1.1);
}

.step.completed .step-number {
  background-color: #10b981;
  color: var(--white);
  border-color: #10b981;
}

.step-label {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-gray);
  text-align: center;
  transition: all 0.3s ease;
  max-width: 100%;
  padding: 0 5px;
}

.step.active .step-label {
  color: var(--text-primary);
  font-weight: 600;
}

.step.completed .step-label {
  color: #10b981;
}

/* Step content styles */
.step-content {
  background-color: var(--bg-white);
  border-radius: var(--border-large-radius);
  box-shadow: var(--box-shadow);
  padding: 30px;
  margin-bottom: 30px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid var(--border-gray);
}

.step-description {
  color: var(--text-gray);
  margin-bottom: 24px;
  font-size: var(--smallfont);
}

.form-section {
  margin-bottom: 24px;
}
#moduleTitle,
#moduleDescription {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  background-color: var(--bg-white);
}
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 20px;
}

.form-group.full-width {
  width: 100%;
}

label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

.field-hint {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  margin-top: 5px;
}

/* Enhanced form fields */
.input-field,
.textarea-field,
.select-field {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  background-color: var(--bg-white);
}

.input-field:focus,
.textarea-field:focus,
.select-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-1px);
}

.textarea-field {
  min-height: 120px;
  resize: vertical;
}

.select-field {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 12px;
  padding-right: 40px;
}

/* File upload styles */
.thumbnail-section {
  margin-top: 30px;
  border-top: 1px solid var(--border-gray);
  padding-top: 24px;
}

.thumbnail-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
}

.thumbnail-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 280px;
  height: 160px;
  border: 2px dashed var(--border-gray);
  border-radius: var(--border-medium-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--bg-gray);
}

.thumbnail-upload:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
  transform: translateY(-2px);
}

.hidden-input {
  display: none;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 20px;
  width: 100%;
  height: 100%;
  justify-content: center;
}

.upload-icon {
  font-size: 28px;
  color: var(--text-gray);
  margin-bottom: 12px;
}

.upload-label span {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.upload-hint {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  text-align: center;
}

.thumbnail-preview {
  position: relative;
  width: 280px;
  height: 160px;
  border-radius: var(--border-medium-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--border-gray);
  transition: all 0.3s ease;
  object-fit: contain;
}

.thumbnail-preview:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-medium);
}

.thumbnail-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: var(--border-medium-radius);
}

.remove-thumbnail {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.remove-thumbnail:hover {
  background-color: #dc2626;
  transform: scale(1.1);
}

/* Modules toggle */
.module-toggle {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
  border-left: 4px solid var(--primary-color);
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-weight: 600;
  color: var(--text-primary);
}

.toggle-button {
  background: none;
  border: none;
  font-size: 28px;
  color: var(--text-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button.active {
  color: #10b981;
}

.toggle-hint {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin-top: 8px;
}

/* Module and content styles */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-gray);
}

.section-header h3 {
  font-size: var(--heading5);
  color: var(--text-primary);
  font-weight: 600;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-button:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

.modules-list,
.content-list,
.quizzes-list {
  margin-top: 16px;
}

/* Enhanced Module Card with Accordion Behavior */
.module-card {
  background-color: var(--bg-white);
  border-radius: var(--border-medium-radius);
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--box-shadow-light);
  transition: all 0.2s ease;
  border: 1px solid var(--border-gray);
}

.module-card:hover {
  box-shadow: var(--box-shadow);
}

.module-header {
  display: flex;
  align-items: center;
  /* margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-gray); */
}

.drag-handle {
  color: var(--text-gray);
  margin-right: 12px;
  cursor: move;
  padding: 8px;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
}

.drag-handle:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.module-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  cursor: pointer;
  transition: all 0.2s ease;
}

.module-title:hover {
  color: var(--primary-color);
}

.module-actions {
  margin-left: auto;
  display: flex;
  gap: 6px;
}

.module-actions button {
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-actions button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.toggle-module-btn {
  color: var(--text-primary) !important;
}

.module-description {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin-bottom: 16px;
  padding-left: 28px;
}

/* Module content/quiz container improvements */
.module-content,
.module-quiz {
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
  padding: 16px;
  margin-top: 12px;
  margin-bottom: 12px;
}

.module-content h5,
.module-quiz h5 {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  /* padding: 12px; */
  /* background-color: var(--bg-white); */
  /* border-radius: var(--border-small-radius); */
  margin-bottom: 8px;
  /* box-shadow: var(--box-shadow-light); */
  transition: all 0.2s ease;
  /* border: 1px solid var(--border-gray); */
}

.quiz-item {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  padding: 12px;
}
.quiz-item-header {
  display: flex;
  align-items: center;
}
#duration {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  background-color: var(--bg-white);
}
.content-item:hover,
.quiz-item:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-1px);
}

.content-item-icon,
.quiz-item-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: var(--border-small-radius);
  margin-right: 16px;
  font-size: 18px;
}

.content-item-info,
.quiz-item-info {
  flex: 1;
}

.content-item-info h4,
.quiz-item-info h4 {
  font-size: var(--basefont);
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.content-item-info p,
.quiz-item-info p {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin-bottom: 4px;
}

.quiz-questions-count {
  display: inline-block;
  padding: 4px 8px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: var(--border-small-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
}

.content-item-actions,
.quiz-item-actions {
  display: flex;
  gap: 8px;
}
.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-item-actions button,
.quiz-item-actions button,
.question-actions .new-edit-question,
.question-actions .new-remove-question {
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-item-actions button:hover,
.quiz-item-actions button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

/* Form editors */
.module-editor,
.content-editor,
.quiz-editor {
  background-color: var(--bg-white);
  border-radius: var(--border-medium-radius);
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: var(--box-shadow);
  border-left: 4px solid var(--primary-color);
}

/* Checkbox group styling */
.checkbox-group {
  margin-top: 16px;
}

.checkbox-container {
  display: flex;

  cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 10px;
  cursor: pointer;
  width: 18px;
  height: 18px;
}

.checkbox-text {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.checkbox-hint {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin-top: 6px;
}

.module-editor h4,
.content-editor h4,
.quiz-editor h4 {
  font-size: var(--heading5);
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-gray);
}

.file-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 8px;
}

.file-upload-label {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.file-upload-label:hover {
  background-color: var(--primary-hover-color);
}

.file-upload-label svg {
  margin-right: 8px;
}

.file-name {
  font-size: var(--smallfont);
  color: var(--text-primary);
  margin-top: 8px;
  padding: 4px 10px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--border-small-radius);
}

.existing-file {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-radius: var(--border-small-radius);
  border: 1px dashed var(--primary-color);
}

.existing-file .file-name {
  background-color: transparent;
  padding: 0;
  margin-top: 0;
  font-weight: 500;
}

.existing-file .file-hint {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  margin-top: 4px;
  margin-bottom: 0;
}

.add-question-form {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-gray);
}

.add-question-form h5 {
  font-size: var(--heading6);
  color: var(--text-primary);
  margin-bottom: 16px;
}

.options-container {
  margin-top: 12px;
  margin-bottom: 16px;
}

.option-row {
  display: flex;
  gap: 12px;
  margin-bottom: 10px;
  align-items: center;
}

.correct-toggle {
  background-color: var(--bg-gray);
  border: 1px solid var(--border-gray);
  color: var(--text-gray);
  padding: 8px 12px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.correct-toggle.active {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

.questions-list {
  margin-top: 24px;
  margin-bottom: 24px;
}

.questions-list h5 {
  font-size: var(--heading6);
  color: var(--text-primary);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.question-item {
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
}

.question-item h6 {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.options-list {
  list-style: none;
  padding: 0;
  margin: 0 0 0 16px;
}

.options-list li {
  padding: 6px 10px;
  margin-bottom: 4px;
  font-size: var(--smallfont);
  color: var(--text-color);
  border-radius: var(--border-small-radius);
}

.options-list li.correct {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  font-weight: 500;
}

.remove-question {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  padding: 4px;
  transition: all 0.2s ease;
}

.remove-question:hover {
  color: #ef4444;
}

/* Form action buttons */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button,
.save-button,
.add-question-button {
  padding: 8px 16px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--border-gray);
  color: var(--text-gray);
}

.cancel-button:hover {
  background-color: var(--bg-gray);
}

.save-button {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: white;
}

.save-button:hover {
  background-color: var(--primary-hover-color);
}

.add-question-button {
  background-color: #10b981;
  border: none;
  color: white;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.add-question-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
}

/* Empty state styling */
.no-modules,
.no-content,
.no-quizzes,
.no-items {
  text-align: center;
  /* padding: 30px; */
  /* background-color: var(--bg-gray); */
  border-radius: var(--border-medium-radius);
  color: var(--text-gray);
  font-size: var(--smallfont);
}

/* Improved navigation buttons */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  padding: 0 10px;
}

.back-nav-button,
.next-button,
.submit-button,
.save-draft-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: var(--border-medium-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-nav-button {
  background-color: var(--bg-white);
  color: var(--text-gray);
  border: 1px solid var(--border-gray);
}

.back-nav-button:hover {
  background-color: var(--bg-gray);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.next-button {
  background-color: var(--primary-color);
  color: white;
  /* margin-left: auto; */
}

.next-button:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.submit-button {
  background-color: #10b981;
  color: white;
  /* margin-left: auto; */
}

.submit-button:hover {
  background-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.save-draft-button {
  background-color: var(--bg-white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  margin-left: auto;
  margin-right: 10px;
}

.save-draft-button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-button:disabled,
.next-button:disabled,
.save-draft-button:disabled {
  background-color: var(--border-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  color: var(--text-gray);
  border-color: var(--border-gray);
}

/* Validation errors */
.validation-errors {
  margin-top: 24px;
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #f87171;
  border-radius: var(--border-medium-radius);
  color: #b91c1c;
}

.validation-errors h3 {
  font-size: var(--basefont);
  margin-bottom: 8px;
  font-weight: 600;
}

.validation-errors ul {
  list-style-type: disc;
  padding-left: 20px;
}

.validation-errors li {
  margin-bottom: 4px;
  font-size: var(--smallfont);
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Review step styling */
.review-section {
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.review-section.valid {
  border-left: 4px solid #10b981;
}

.review-section.invalid {
  border-left: 4px solid #ef4444;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-gray);
  background-color: var(--bg-gray);
  border-top-left-radius: var(--border-medium-radius);
  border-top-right-radius: var(--border-medium-radius);
}

.review-header h3 {
  font-size: var(--heading5);
  color: var(--text-primary);
  font-weight: 600;
}

.valid-icon {
  color: #10b981;
}

.invalid-icon {
  color: #ef4444;
}

.review-content {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.review-details {
  flex: 1;
}

.review-details.wide {
  width: 100%;
}

.review-item {
  margin-bottom: 16px;
}

.label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 8px;
  font-size: var(--basefont);
}

.value {
  color: var(--text-color);
  font-size: var(--basefont);
}

.validation-messages {
  padding: 16px 20px;
  background-color: #fee2e2;
  color: #b91c1c;
  border-bottom-left-radius: var(--border-medium-radius);
  border-bottom-right-radius: var(--border-medium-radius);
}

.validation-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: var(--smallfont);
}

.submission-readiness {
  padding: 30px;
  border-radius: var(--border-medium-radius);
  margin-top: 32px;
  text-align: center;
  background-color: var(--bg-gray);
}

.ready-message {
  color: #10b981;
  font-size: var(--heading6);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.not-ready-message {
  color: #b91c1c;
  font-size: var(--heading6);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.server-errors {
  margin-top: 20px;
  padding: 16px;
  background-color: #fee2e2;
  border-radius: var(--border-small-radius);
  border-left: 4px solid #ef4444;
}

.server-errors h4 {
  color: #b91c1c;
  margin-bottom: 12px;
  font-size: var(--basefont);
  font-weight: 600;
}

.server-errors ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.server-errors .error-item {
  margin-bottom: 8px;
  font-size: var(--smallfont);
  padding: 8px 12px;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: var(--border-small-radius);
}

.server-errors .error-item strong {
  color: #b91c1c;
  margin-right: 5px;
}

/* CurriculumStep Component Styles */
.curriculum-step {
  /* padding: 20px;
  background-color: #fff;
  border-radius: var(--border-large-radius);
  box-shadow: var(--box-shadow-light); */
}

.curriculum-step h2 {
  color: var(--text-primary);
  margin-bottom: 10px;
  font-weight: 600;
}

/* Module Content and Quiz */
.module-content,
.module-quiz {
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
  padding: 16px;
  margin-top: 16px;
}

.module-content h5,
.module-quiz h5 {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0px;
  display: flex;
  align-items: center;
}

.content-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.content-title {
  margin-left: 8px;
  font-weight: 500;
}

.content-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
.content-actions .edit-grey-btn,
.delete-grey-btn {
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-button,
.delete-button {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.edit-button {
  color: var(--primary-color);
}

.delete-button {
  color: #dc3545;
}

.edit-button:hover,
.delete-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.add-content-button,
.add-quiz-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
  color: var(--primary-color);
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  justify-content: center;
  margin-top: 12px;
  transition: all 0.2s ease;
}

/* Quiz Container */
.quiz-container {
  margin-top: 10px;
}

.no-quiz {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 0px;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
}

.no-quiz p {
  color: var(--text-gray);
}

/* Content/Quiz editors inside modules */
.content-editor.in-module,
.quiz-editor.in-module {
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
  padding: 16px;
  margin: 16px 0;
  border-left: 3px solid var(--primary-color);
}

.content-editor.in-module h4,
.quiz-editor.in-module h4 {
  color: var(--text-primary);
  font-size: var(--basefont);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-gray);
}

/* Content type badge */
.content-type-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  margin-left: 8px;
}

/* Improved content info */
.content-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.content-title {
  margin-left: 8px;
  font-weight: 500;
}

/* Content/Quiz form improvements */
.quiz-editor .form-group,
.content-editor .form-group {
  margin-bottom: 16px;
}

.quiz-editor label,
.content-editor label {
  display: block;
  display: flex;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: var(--smallfont);
}

.quiz-editor input[type="text"],
.quiz-editor textarea,
.content-editor input[type="text"],
.content-editor textarea,
.content-editor select {
  width: 100%;
  padding: 10px 12px;
  border-radius: var(--border-small-radius);
  border: 1px solid var(--border-gray);
  background-color: var(--bg-white);
  font-size: var(--smallfont);
}

.quiz-editor input[type="text"]:focus,
.quiz-editor textarea:focus,
.content-editor input[type="text"]:focus,
.content-editor textarea:focus,
.content-editor select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
  outline: none;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .stepper-container {
    padding: 0 5px;
  }

  .step-label {
    font-size: var(--extrasmallfont);
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .next-button,
  .submit-button,
  .back-nav-button {
    width: 100%;
    justify-content: center;
  }

  .next-button,
  .submit-button {
    margin-left: 0;
  }

  .step-content {
    padding: 20px 15px;
  }

  .thumbnail-container {
    flex-direction: column;
  }

  .thumbnail-preview,
  .thumbnail-upload {
    width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 500px) {
  .step-number {
    width: 32px;
    height: 32px;
    font-size: var(--extrasmallfont);
  }

  .stepper::before {
    top: 16px;
  }
}

/* Course info step heading */
.course-info-step h2 {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.course-settings-step h2 {
  color: var(--text-primary);
  font-size: var(--heading3);
  font-weight: 600;
  margin-bottom: 12px;
}

.settings-card {
  background-color: var(--bg-white);
  border-radius: var(--border-medium-radius);
  margin-bottom: 24px;
  padding: 30px;
  border: 1px solid var(--border-gray);
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.settings-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
}

.settings-card:hover {
  box-shadow: var(--box-shadow);
  border-color: var(--primary-color-light);
  transform: translateY(-2px);
}

.settings-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.settings-card-icon {
  font-size: 24px;
  color: var(--primary-color);
  margin-right: 12px;
}

.settings-card-title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-primary);
}

.toggle-group {
  margin-top: 20px;
}

.toggle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background-color: var(--bg-gray-light);
  border-radius: var(--border-medium-radius);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.toggle-item:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: var(--primary-color-light);
}

.toggle-info {
  flex: 1;
}

.toggle-info h3 {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.toggle-info p {
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: var(--border-small-radius);
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button span {
  font-weight: 600;
}

.toggle-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-light);
}

.toggle-button.active {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
  color: #10b981;
}

.toggle-button.active span {
  color: #10b981;
}

.toggle-button:not(.active) {
  background-color: rgba(107, 114, 128, 0.1);
  border-color: var(--border-gray);
  color: var(--text-gray);
}

/* Duration Selector Styling */
.duration-selector {
  position: relative;
}

.duration-selector select {
  appearance: none;
  width: 100%;
  padding: 14px 16px;
  font-size: var(--basefont);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  background-color: var(--bg-white);
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: var(--box-shadow-light);
}

.duration-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.duration-selector::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--text-gray);
  pointer-events: none;
}

.duration-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 5px 10px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: var(--smallfont);
  font-weight: 500;
  margin-top: 10px;
}

/* Time Commitment Container Styling */
.time-commitment-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.time-value-input {
  /* flex: 1; */
}

.time-unit-selector {
  width: 120px;
  position: relative;
}

.time-unit-selector select {
  appearance: none;
  width: 100%;
  padding: 12px 16px;
  font-size: var(--basefont);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  background-color: var(--bg-white);
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: var(--box-shadow-light);
}

.time-unit-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.time-unit-selector::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--text-gray);
  pointer-events: none;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-right: 10px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: var(--smallfont);
  font-weight: 600;
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* SchoolJXS-style toggle button */
.schooljxs-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.schooljxs-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #10b981;
}

input:focus + .slider {
  box-shadow: 0 0 1px #10b981;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.toggle-label-text {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 10px;
}

.toggle-wrapper {
  display: flex;
  align-items: center;
}

/* Access Settings Step Styling */
.access-settings-step {
  max-width: 800px;
  margin: 0 auto;
}

.access-settings-step h2 {
  color: var(--text-primary);
  font-size: var(--heading3);
  font-weight: 600;
  margin-bottom: 12px;
}

.enrolled-users-section {
  margin-top: 30px;
}

.enrolled-users-section h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.section-description {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin-bottom: 16px;
}

.user-search {
  margin-bottom: 24px;
  position: relative;
}

.user-search input {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  font-size: var(--basefont);
  background-color: var(--bg-white);
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.user-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow);
  z-index: 10;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.user-result {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-result:last-child {
  border-bottom: none;
}

.user-result:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.user-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--basefont);
}

.user-email {
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.user-status {
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.add-user-button {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-user-button:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: scale(1.1);
}

.no-results {
  padding: 16px;
  text-align: center;
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.enrolled-users-list {
  background-color: var(--bg-gray-light);
  border-radius: var(--border-medium-radius);
  padding: 20px;
  border: 1px solid var(--border-gray);
}

.enrolled-users-list h4 {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hint {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
  font-weight: normal;
}

.no-users {
  text-align: center;
  padding: 20px;
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.enrolled-users-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.enrolled-user {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--bg-white);
  border-radius: var(--border-small-radius);
  margin-bottom: 8px;
  transition: all 0.2s ease;
  border: 1px solid var(--border-gray);
}

.enrolled-user:hover {
  border-color: var(--primary-color-light);
  box-shadow: var(--box-shadow-light);
  transform: translateY(-2px);
}

.remove-user-button {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.remove-user-button:hover {
  background-color: #ef4444;
  color: var(--white);
}

.progress-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 10px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  margin-left: 8px;
}

/* Quiz question editing styles */
.question-actions {
  display: flex;
  gap: 8px;
}

.edit-question,
.remove-question {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-question {
  color: #4a6cf7;
}

.edit-question:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.remove-question {
  color: #f74a4a;
}

.remove-question:hover {
  background-color: rgba(247, 74, 74, 0.1);
}

.question-form-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.cancel-edit-button {
  background-color: #f0f0f0;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border-radius: var(--border-small-radius);
}

.cancel-edit-button:hover {
  background-color: #e0e0e0;
}
