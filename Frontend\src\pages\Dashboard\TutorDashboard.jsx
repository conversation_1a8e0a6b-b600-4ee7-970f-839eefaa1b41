import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  FaBook,
  FaGraduationCap,
  FaClock,
  FaCalendarAlt,
  FaEye,
  FaUserGraduate,
} from "react-icons/fa";
import {
  getMyCoursesThunk,
  getCoursesThunk,
} from "../../redux/educator/educatorSlice";
import DataTableComponent from "../../components/DataTable";
import ProgressBar from "../../components/common/ProgressBar";
import "../../assets/styles/TutorDashboard.css";
const VITE_IMAGE_URL = import.meta.env.VITE_IMAGE_URL;

const TutorDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [coursesData, setCoursesData] = useState([]);
  const [ongoingCourses, setOngoingCourses] = useState([]);
  const [allCourses, setAllCourses] = useState([]);

  // Get courses data from Redux store
  const { myCourses, courses, loading } = useSelector(
    (state) => state.educator
  );
  const { user } = useSelector((state) => state.auth);

  // Update loading state when Redux loading state changes
  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(getMyCoursesThunk());
    dispatch(getCoursesThunk());
  }, [dispatch]);

  // Process all courses data when it changes
  useEffect(() => {
    if (courses && courses.length > 0) {
      setAllCourses(courses);
    }
  }, [courses]);

  // Process enrolled courses data when it changes
  useEffect(() => {
    if (myCourses && myCourses.length > 0) {
      // Get current user ID
      const currentUserId = user?.id;

      const formattedCourses = myCourses.map((course) => {
        // Find the user's enrollment
        let userProgress = 0;
        let enrollmentStatus = "in_progress";
        let enrollmentDate = new Date();

        if (course.enrolledUsers && course.enrolledUsers.length > 0) {
          // Find the user's enrollment
          const userEnrollment = course.enrolledUsers.find((enrollment) => {
            // Handle different possible data formats of user id
            const enrollmentUserId =
              typeof enrollment.user === "object"
                ? enrollment.user._id
                : enrollment.user;

            return enrollmentUserId === currentUserId;
          });

          if (userEnrollment) {
            userProgress = userEnrollment.progress || 0;
            enrollmentStatus = userEnrollment.status || "in_progress";
            enrollmentDate = new Date(userEnrollment.enrolledAt || Date.now());
          }
        }

        return {
          id: course._id,
          title: course.title || "Untitled Course",
          category: course.category || "Uncategorized",
          professor: course.creator?.name || "Unknown",
          duration: course.duration || "N/A",
          description: course.description || "No description available",
          tags: course.tags?.join(", ") || "No tags",
          language: course.language || "English",
          status: course.status === 1,
          thumbnail:
            course.thumbnail ||
            "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80",
          hasModules: course.hasModules || false,
          isEnrolled: true, // These are all enrolled courses since we're using getMyCourses
          progress: userProgress,
          enrollmentStatus: enrollmentStatus,
          startDate: enrollmentDate.toLocaleDateString(),
        };
      });

      setCoursesData(formattedCourses);

      // Filter for ongoing courses (status is "in_progress")
      const ongoing = formattedCourses.filter(
        (course) => course.enrollmentStatus === "in_progress"
      );
      setOngoingCourses(ongoing);
    }
  }, [myCourses, user]);

  // Count courses by status
  const getCompletedCourses = () => {
    // Only count enrolled courses with "completed" status
    return coursesData.filter(
      (course) => course.enrollmentStatus === "completed"
    ).length;
  };

  const getOngoingCourses = () => {
    // Only count enrolled courses with "in_progress" status
    return coursesData.filter(
      (course) => course.enrollmentStatus === "in_progress"
    ).length;
  };

  const getTotalCourses = () => {
    // Return the total number of all courses in the system
    return allCourses.length;
  };

  // Handle card clicks
  const handleTotalCoursesClick = () => {
    navigate("/dashboard/tutor/courses");
  };

  const handleOngoingCoursesClick = () => {
    navigate("/dashboard/tutor/courses");
  };

  const handleCompletedCoursesClick = () => {
    navigate("/dashboard/tutor/courses");
  };

  // View course handler
  const handleViewCourse = (row) => {
    navigate(`/dashboard/tutor/courses/${row.id}`);
  };

  // Table columns configuration
  const columns = [
    {
      name: "Course",
      cell: (row) => (
        <div className="course-info">
          <img
            src={VITE_IMAGE_URL + row.thumbnail}
            alt={row.title}
            className="course-thumbnail"
          />
          <div className="course-details">
            <span className="course-title">{row.title}</span>
            <span className="course-category">{row.category}</span>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      name: "Started On",
      selector: (row) => row.startDate,
      sortable: true,
    },
    {
      name: "Progress",
      cell: (row) => (
        <div className="progress-container">
          <ProgressBar
            percentage={row.progress}
            size="small"
            color="primary"
            animated={false}
          />
        </div>
      ),
      sortable: true,
    },
    {
      name: "Actions",
      cell: (row) => (
        <div className="action-buttons">
          <button
            className="action-btn view"
            onClick={() => handleViewCourse(row)}
            title="View Course"
          >
            <FaEye />
          </button>
        </div>
      ),
      width: "100px",
      center: true,
    },
  ];

  return (
    <div className="tutor-dashboard">
      {isLoading ? (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Loading dashboard data...</p>
        </div>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="dashboard-stats">
            <div className="stat-card total" onClick={handleTotalCoursesClick}>
              <div className="stat-icon3">
                <FaBook size={24} />
                <FaBook className="icondesign3" />
              </div>
              <div>
                <div className="stat-count">{getTotalCourses()}</div>
                <div className="stat-title">Total Courses</div>
              </div>
            </div>

            <div
              className="stat-card ongoing"
              onClick={handleOngoingCoursesClick}
            >
              <div className="stat-icon1">
                <FaClock size={24} />

                <FaClock className="icondesign1" />
              </div>
              <div>
                <div className="stat-count">{getOngoingCourses()}</div>
                <div className="stat-title">Ongoing Courses</div>
              </div>
            </div>

            <div
              className="stat-card completed"
              onClick={handleCompletedCoursesClick}
            >
              <div className="stat-icon2">
                <FaGraduationCap size={24} />
                <FaGraduationCap className="icondesign2" />
              </div>
              <div>
                <div className="stat-count">{getCompletedCourses()}</div>
                <div className="stat-title">Completed Courses</div>
              </div>
            </div>
          </div>

          {/* Ongoing Courses Section */}
          <div className="dashboard-section">
            <div className="section-header">
              <h2 className="section-title">My Ongoing Courses</h2>
              <button
                className="view-all-btn"
                onClick={() => navigate("/dashboard/tutor/courses")}
              >
                View All Courses
              </button>
            </div>

            {ongoingCourses.length > 0 ? (
              <div className="courses-table">
                <DataTableComponent
                  columns={columns}
                  data={ongoingCourses}
                  showSearch={false}
                />
              </div>
            ) : (
              <div className="empty-courses">
                <div className="empty-message">
                  <FaBook className="empty-icon" />
                  <h3>No ongoing courses</h3>
                  <p>You don't have any courses in progress right now.</p>
                  <button
                    className="browse-courses-btn"
                    onClick={() => navigate("/dashboard/tutor/courses")}
                  >
                    Browse Courses
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TutorDashboard;
