@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Coiny&display=swap");
@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: var(--font-Poppins);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
}
p {
  margin: 0;
}
/* Root Variables */
:root {
  /* Colors */
  --primary-color: #1a2e6e;
  --primary-color-rgb: 26, 46, 110; /* RGB values for #1a2e6e */
  --primary-hover-color: #2a3f80; /* Slightly lighter shade for hover */
  --primary-light-color: #3a4f90; /* Lighter shade of primary color */
  --primary-light-bg: #e8eaf6;

  --text-color: #1c1c1c;
  --text-primary: #1a2e6e;
  --text-gray: #84868a;

  --bg-gray: #f5f7f9;
  --bg-white: #ffffff;
  --bg-primary: #1a2e6e;
  --border-gray: #f0edff;
  --dark-gray: #c3c3c3;
  --white: #ffffff; /* ← Add this line */

  /* Font Sizes */
  --heading1: 50px;
  --heading2: 40px;
  --heading3: 30px;
  --heading4: 22px;
  --heading5: 20px;
  --heading6: 18px;
  --basefont: 16px;
  --smallfont: 14px;
  --extrasmallfont: 12px;
  /* Box Model Properties */

  --border-large-radius: 12px;
  --border-medium-radius: 10px;
  --border-medium-small-radius: 8px;
  --border-small-radius: 4px;

  /* Shadows */
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
  --box-shadow-dark: 0 6px 8px rgba(0, 0, 0, 0.2);
  /* Z-Index */
  --z-index-modal: 1000;
  --z-index-tooltip: 1100;
  --z-index-header: 900;

  /* font-family */
  --font-Coiny: "Poppins", sans-serif;
  --font-Poppins: "Poppins", sans-serif;
}
/* Container Styling */
.content-width {
  width: 100%;
  margin: auto;
}
/* Input Styling */
input[type="checkbox"],
input[type="radio"] {
  accent-color: var(--primary-color);
  width: 18px;
  height: 18px;
}
input,
select {
  outline: none;
}
/* Text Selection */
::selection {
  color: var(--white) !important;
  background-color: var(--primary-color);
}
/* Section Padding */
.section-padding {
  padding-top: 50px;
  padding-bottom: 50px;
}
/* Common Titles and Descriptions */
.commontitle {
  text-align: center;
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 10px;
}
.commondescription {
  text-align: center;
  font-size: var(--heading6);
  margin-bottom: 30px;
}
/* Flexbox Centering */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
/* Error Message */
.error-message {
  color: red;
  font-size: var(--smallfont);
  font-weight: 400;
}
/* Responsive Media Queries */
/* Tablets (Portrait & Landscape) */
@media (max-width: 1024px) {
  :root {
    --heading1: 44px;
    --heading2: 36px;
    --heading3: 28px;
    --heading4: 20px;
    --heading5: 18px;
    --heading6: 16px;
    --basefont: 15px;
    --smallfont: 13px;
    --extrasmallfont: 11px;
  }
  .section-padding {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}
/* Large Smartphones (Portrait & Landscape) */
@media (max-width: 768px) {
  :root {
    --heading1: 36px;
    --heading2: 28px;
    --heading3: 22px;
    --heading4: 17px;
    --heading5: 16px;
    --heading6: 14px;
    --basefont: 13px;
    --smallfont: 11px;
    --extrasmallfont: 10px;
  }
  .section-padding {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

/* Small Smartphones (Landscape) */
@media (max-width: 640px) and (orientation: landscape) {
  :root {
    --heading1: 36px;
    --heading2: 28px;
    --heading3: 22px;
    --heading4: 17px;
    --heading5: 16px;
    --heading6: 14px;
    --basefont: 13px;
    --smallfont: 11px;
    --extrasmallfont: 14px;
  }
  .section-padding {
    padding-top: 25px;
    padding-bottom: 25px;
  }
}

/* Small Smartphones (Portrait) */
@media (max-width: 576px) and (orientation: portrait) {
  :root {
    --heading1: 36px;
    --heading2: 28px;
    --heading3: 22px;
    --heading4: 17px;
    --heading5: 16px;
    --heading6: 14px;
    --basefont: 13px;
    --smallfont: 11px;
    --extrasmallfont: 14px;
  }
  .section-padding {
    padding-top: 25px;
    padding-bottom: 25px;
  }
}
/* Extra Small Devices (Watches, Foldable Phones) */

/* Scroll to Top Button */
.scroll-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 9999;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  font-size: 18px;
}

.scroll-to-top-btn.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-3px);
}

/* Smooth scrolling for the entire page */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

/* Loading Spinner Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Border utility classes for spinner */
.border-2 {
  border-width: 2px;
  border-style: solid;
}

.border-3 {
  border-width: 3px;
  border-style: solid;
}

.border-4 {
  border-width: 4px;
  border-style: solid;
}

.border-t-primary-color {
  border-top-color: var(--primary-color);
}

/* Spacing utility classes */
.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Size utility classes */
.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

/* Summernote editor styles */
.summernote-editor-container {
  margin-bottom: 20px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.summernote-editor-container .note-editor {
  border-radius: 4px;
}

.summernote-editor-container .note-toolbar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ced4da;
  border-radius: 4px 4px 0 0;
}

.summernote-editor-container .note-statusbar {
  background-color: #f8f9fa;
  border-top: 1px solid #ced4da;
  border-radius: 0 0 4px 4px;
}

/* Text content display styles */
.text-content-preview {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 20px;
  margin-top: 15px;
  background-color: #fff;
}

.html-content {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  line-height: 1.6;
}

.html-content h1,
.html-content h2,
.html-content h3,
.html-content h4,
.html-content h5,
.html-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.html-content p {
  margin-bottom: 1rem;
}

.html-content ul,
.html-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.html-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 4px;
}

.html-content blockquote {
  padding: 1rem;
  border-left: 4px solid #ced4da;
  background-color: #f8f9fa;
  margin: 1rem 0;
}

.html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.html-content table th,
.html-content table td {
  border: 1px solid #ced4da;
  padding: 0.5rem;
}

.html-content a {
  color: #007bff;
  text-decoration: none;
}

.html-content a:hover {
  text-decoration: underline;
}

/* Content type icons */
.content-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.content-type-badge {
  background-color: #f0f0f0;
  border-radius: 12px;
  padding: 2px 10px;
  font-size: 0.8rem;
  margin-left: 10px;
}
.certificatebgcolor {
  background-color: var(--primary-color);
}
